/**
 * Test utility for Admin Order API
 * This file can be used to test the admin order service functions
 */

import adminOrderService from '../services/adminOrderService';

export const testAdminOrderAPI = async () => {
  try {

    // Test 1: Get all orders
    const ordersResponse = await adminOrderService.getAllOrders({
      page: 1,
      limit: 5
    });

    // Test 2: Get order stats
    const statsResponse = await adminOrderService.getOrderStats();

    // Test 3: Get order analytics
    const analyticsResponse = await adminOrderService.getOrderAnalytics({
      period: '30d',
      groupBy: 'day'
    });

    return true;

  } catch (error) {
    console.error('API Test Error:', error);
    console.error('Error Details:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
    return false;
  }
};

// Export individual test functions for specific testing
export const testGetAllOrders = async (params = {}) => {
  try {
    const response = await adminOrderService.getAllOrders(params);
    return response;
  } catch (error) {
    console.error('getAllOrders test error:', error);
    throw error;
  }
};

export const testGetOrderById = async (orderId) => {
  try {
    const response = await adminOrderService.getOrderById(orderId);
    return response;
  } catch (error) {
    console.error('getOrderById test error:', error);
    throw error;
  }
};

export const testUpdateOrderStatus = async (orderId, statusData) => {
  try {
    const response = await adminOrderService.updateOrderStatus(orderId, statusData);
    return response;
  } catch (error) {
    console.error('updateOrderStatus test error:', error);
    throw error;
  }
};

export default {
  testAdminOrderAPI,
  testGetAllOrders,
  testGetOrderById,
  testUpdateOrderStatus
};
